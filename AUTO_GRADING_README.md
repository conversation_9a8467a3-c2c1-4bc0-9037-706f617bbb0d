# Hệ Thống Chấm Điểm Tự Động - PlanBook AI

## Tổng Quan

Hệ thống chấm điểm tự động sử dụng OpenCV để xử lý phiếu trả lời trắc nghiệm, hỗ trợ:
- **<PERSON><PERSON> báo danh** (8 chữ số)
- **Mã đề thi** (4 chữ số) 
- **40 câu trắc nghiệm** phần 1 (A, B, C, D)

## Tính Năng

### 🔧 Hai Bộ Xử Lý

1. **Basic Auto Grading Service** (`/grade`)
   - Xử lý cơ bản với OpenCV
   - <PERSON><PERSON> hợp cho ảnh chất lượng tốt

2. **Enhanced OMR Processor** (`/grade-enhanced`)
   - Dựa trên thuật toán MiAI_Auto_Grading
   - Perspective correction tự động
   - Phát hiện bubble chính xác hơn
   - Tính toán fill ratio để xác định bubble được chọn
   - Xử lý đa dạng loại ảnh

### 🎯 Khả Năng Xử Lý

- **Định dạng ảnh hỗ trợ**: JPG, JPEG, PNG, BMP, TIFF
- **Định dạng đáp án**: Excel (.xlsx, .xls)
- **Xử lý hàng loạt**: Nhiều ảnh cùng lúc
- **Thống kê chi tiết**: Điểm số, độ chính xác, phân tích lỗi

## Cấu Trúc File Excel Đáp Án

File Excel cần có cấu trúc như sau:

| Mã Đề | Câu 1 | Câu 2 | Câu 3 | ... | Câu 40 |
|-------|-------|-------|-------|-----|--------|
| 001   | A     | B     | C     | D   | A      |
| 002   | B     | C     | D     | A   | B      |
| 003   | C     | D     | A     | B   | C      |
| 004   | D     | A     | B     | C   | D      |

## API Endpoints

### 1. Health Check
```
GET /api/v1/auto_grading/health
```
Kiểm tra trạng thái hệ thống và các tính năng có sẵn.

### 2. Chấm Điểm Cơ Bản
```
POST /api/v1/auto_grading/grade
```
**Parameters:**
- `image_files`: Danh sách file ảnh bài làm
- `excel_file`: File Excel chứa đáp án

### 3. Chấm Điểm Nâng Cao
```
POST /api/v1/auto_grading/grade-enhanced
```
**Parameters:**
- `image_files`: Danh sách file ảnh bài làm  
- `excel_file`: File Excel chứa đáp án

## Cách Sử Dụng

### 1. Khởi Động Hệ Thống
```bash
# Khởi động FastAPI server
fastapi dev app/main.py

# Hoặc sử dụng script
./start_fastapi.bat
```

### 2. Test Hệ Thống
```bash
# Chạy test tự động
python test_auto_grading.py
```

### 3. Sử dụng qua Web Interface
Truy cập: `http://localhost:8000/docs` để sử dụng Swagger UI

### 4. Sử dụng qua Python
```python
import requests

# Upload files
files = {
    'excel_file': open('data/sample_answer_keys_omr.xlsx', 'rb'),
    'image_files': [
        open('image1.jpg', 'rb'),
        open('image2.jpg', 'rb')
    ]
}

# Gọi API chấm điểm nâng cao
response = requests.post(
    'http://localhost:8000/api/v1/auto_grading/grade-enhanced',
    files=files
)

result = response.json()
print(f"Điểm trung bình: {result['summary']['average_score']}")
```

## Kết Quả Trả Về

### Cấu Trúc Response
```json
{
  "message": "Chấm điểm nâng cao hoàn thành",
  "processor": "Enhanced OMR Processor (MiAI-based)",
  "summary": {
    "total_processed": 5,
    "successful": 4,
    "failed": 1,
    "average_score": 7.25,
    "total_bubbles_detected": 800,
    "avg_bubbles_per_sheet": 160.0
  },
  "results": [...]
}
```

### Chi Tiết Từng Bài Làm
```json
{
  "filename": "student_001.jpg",
  "student_id": "12345678",
  "test_code": "001",
  "bubbles_detected": 160,
  "grading": {
    "score": 8.5,
    "correct_count": 34,
    "incorrect_count": 6,
    "blank_count": 0,
    "total_questions": 40,
    "accuracy_percentage": 85.0,
    "details": [...]
  },
  "status": "success"
}
```

## Files và Thư Mục

```
├── app/
│   ├── services/
│   │   ├── auto_grading_service.py      # Service chấm điểm cơ bản
│   │   └── enhanced_omr_processor.py    # Service chấm điểm nâng cao
│   └── api/endpoints/
│       └── auto_grading.py              # API endpoints
├── data/
│   ├── sample_answer_keys_omr.xlsx      # File đáp án mẫu
│   └── test_images/                     # Thư mục ảnh test
├── test_auto_grading.py                 # Script test hệ thống
└── create_sample_excel.py               # Script tạo file Excel mẫu
```

## Lưu Ý Quan Trọng

### 📸 Chất Lượng Ảnh
- Ảnh cần rõ nét, không bị mờ
- Phiếu trả lời phải nằm trong khung ảnh
- Tránh bóng đổ và ánh sáng không đều
- Độ phân giải tối thiểu: 800x600 pixels

### 🎯 Độ Chính Xác
- Enhanced OMR Processor có độ chính xác cao hơn
- Hệ thống tự động phát hiện và căn chỉnh phiếu trả lời
- Fill ratio > 40% được coi là bubble được chọn

### ⚠️ Xử Lý Lỗi
- Nếu không phát hiện được mã đề, sử dụng đáp án đầu tiên
- Nếu không đọc được số báo danh, sử dụng "00000000"
- Bubble không rõ ràng được đánh dấu là câu bỏ trống

## Tham Khảo

Hệ thống dựa trên:
- [MiAI_Auto_Grading](https://github.com/thangnch/MiAI_Auto_Grading) - Thuật toán OMR cơ bản
- OpenCV - Xử lý ảnh và computer vision
- FastAPI - Web framework
- Pandas - Xử lý dữ liệu Excel

## Hỗ Trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra logs trong thư mục `logs/`
2. Chạy health check: `GET /api/v1/auto_grading/health`
3. Test với file mẫu trước khi sử dụng dữ liệu thực
