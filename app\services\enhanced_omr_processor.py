import cv2
import numpy as np
import pandas as pd
import math
from typing import List, Dict, Tuple, Optional
import logging
from pathlib import Path
import tempfile
import os

logger = logging.getLogger(__name__)


class EnhancedOMRProcessor:
    """
    Enhanced OMR Processor dựa trên MiAI_Auto_Grading
    Tối ưu hóa cho việc xử lý phiếu trả lời trắc nghiệm Việt Nam
    """

    def __init__(self):
        self.answer_choices = ["A", "B", "C", "D"]

    def distance(self, p1: Tuple[int, int], p2: Tuple[int, int]) -> float:
        """Tính khoảng cách Euclidean giữa hai điểm"""
        return math.sqrt(((p1[0] - p2[0]) ** 2) + ((p1[1] - p2[1]) ** 2))

    def sort_contours(self, cnts, method="left-to-right"):
        """Sắp xếp contours theo phư<PERSON>ng pháp chỉ định"""
        reverse = False
        i = 0

        if method == "right-to-left" or method == "bottom-to-top":
            reverse = True

        if method == "top-to-bottom" or method == "bottom-to-top":
            i = 1

        bounding_boxes = [cv2.boundingRect(c) for c in cnts]
        (cnts, bounding_boxes) = zip(
            *sorted(zip(cnts, bounding_boxes), key=lambda b: b[1][i], reverse=reverse)
        )

        return cnts, bounding_boxes

    def order_points(self, pts):
        """Sắp xếp 4 điểm theo thứ tự: top-left, top-right, bottom-right, bottom-left"""
        rect = np.zeros((4, 2), dtype="float32")

        s = pts.sum(axis=1)
        rect[0] = pts[np.argmin(s)]  # top-left
        rect[2] = pts[np.argmax(s)]  # bottom-right

        diff = np.diff(pts, axis=1)
        rect[1] = pts[np.argmin(diff)]  # top-right
        rect[3] = pts[np.argmax(diff)]  # bottom-left

        return rect

    def four_point_transform(self, image, pts):
        """Thực hiện perspective transform để căn chỉnh tài liệu"""
        rect = self.order_points(pts)
        (tl, tr, br, bl) = rect

        # Tính chiều rộng mới
        width_a = np.sqrt(((br[0] - bl[0]) ** 2) + ((br[1] - bl[1]) ** 2))
        width_b = np.sqrt(((tr[0] - tl[0]) ** 2) + ((tr[1] - tl[1]) ** 2))
        max_width = max(int(width_a), int(width_b))

        # Tính chiều cao mới
        height_a = np.sqrt(((tr[0] - br[0]) ** 2) + ((tr[1] - br[1]) ** 2))
        height_b = np.sqrt(((tl[0] - bl[0]) ** 2) + ((tl[1] - bl[1]) ** 2))
        max_height = max(int(height_a), int(height_b))

        # Điểm đích cho perspective transform
        dst = np.array(
            [
                [0, 0],
                [max_width - 1, 0],
                [max_width - 1, max_height - 1],
                [0, max_height - 1],
            ],
            dtype="float32",
        )

        # Thực hiện perspective transform
        M = cv2.getPerspectiveTransform(rect, dst)
        warped = cv2.warpPerspective(image, M, (max_width, max_height))

        return warped

    def find_corner_by_rotated_rect(self, box, approx):
        """Tìm góc dựa trên rotated rectangle và approximation"""
        corner = []
        for p_box in box:
            min_dist = float("inf")
            min_p = None
            for p in approx:
                dist = self.distance(p_box, p[0])
                if dist < min_dist:
                    min_dist = dist
                    min_p = p[0]
            corner.append(min_p)

        return np.array(corner)

    def preprocess_image(self, image):
        """Tiền xử lý ảnh theo phương pháp MiAI"""
        # Chuyển sang grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Gaussian blur để giảm noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Adaptive threshold để tạo binary image
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 31, 3
        )

        return gray, blurred, thresh

    def detect_document_contour(self, thresh):
        """Phát hiện contour của tài liệu (phiếu trả lời)"""
        # Tìm contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)

        # Sắp xếp theo diện tích giảm dần
        contours = sorted(contours, key=lambda x: cv2.contourArea(x), reverse=True)

        # Tìm contour có 4 góc (tài liệu)
        for contour in contours[:10]:  # Kiểm tra 10 contour lớn nhất
            peri = cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, 0.01 * peri, True)

            # Nếu tìm thấy contour có 4 góc
            if len(approx) == 4:
                return contour, approx

        # Nếu không tìm thấy, sử dụng contour lớn nhất
        if len(contours) > 0:
            contour = (
                contours[1] if len(contours) > 1 else contours[0]
            )  # Bỏ qua contour đầu tiên (có thể là toàn bộ ảnh)
            rect = cv2.minAreaRect(contour)
            box = cv2.boxPoints(rect)
            approx = np.array([[point] for point in box], dtype=np.int32)
            return contour, approx

        return None, None

    def detect_answer_bubbles(self, warped_thresh, min_area=30, max_area=200):
        """Phát hiện các ô tròn trả lời (bubbles)"""
        # Tìm contours trong ảnh đã căn chỉnh
        contours, _ = cv2.findContours(
            warped_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )

        bubble_contours = []

        for contour in contours:
            # Kiểm tra diện tích
            area = cv2.contourArea(contour)
            if min_area <= area <= max_area:
                # Kiểm tra tỷ lệ khung hình (gần hình vuông/tròn)
                (_, _, w, h) = cv2.boundingRect(contour)
                aspect_ratio = w / float(h)

                # Chỉ chấp nhận những contour có tỷ lệ gần 1:1
                if 0.8 <= aspect_ratio <= 1.2:
                    bubble_contours.append(contour)

        return bubble_contours

    def extract_answers_from_bubbles(
        self, bubble_contours, warped_thresh, num_questions=40
    ):
        """Trích xuất câu trả lời từ các bubble contours"""
        if not bubble_contours:
            return [""] * num_questions

        # Sắp xếp bubbles theo vị trí từ trên xuống dưới
        bubble_contours = self.sort_contours(bubble_contours, method="top-to-bottom")[0]

        answers = []

        # Xử lý theo từng hàng (giả định 5 câu mỗi hàng, 8 hàng cho 40 câu)
        questions_per_row = 5
        num_rows = (num_questions + questions_per_row - 1) // questions_per_row

        for row in range(num_rows):
            # Lấy bubbles của hàng hiện tại
            start_idx = row * questions_per_row * 4  # 4 lựa chọn cho mỗi câu
            end_idx = min(start_idx + questions_per_row * 4, len(bubble_contours))

            if start_idx >= len(bubble_contours):
                # Thêm câu trả lời trống cho các câu còn lại
                remaining_questions = num_questions - len(answers)
                answers.extend([""] * remaining_questions)
                break

            row_bubbles = bubble_contours[start_idx:end_idx]

            # Sắp xếp theo chiều ngang
            row_bubbles = self.sort_contours(row_bubbles, method="left-to-right")[0]

            # Xử lý từng câu hỏi trong hàng
            for q in range(questions_per_row):
                if len(answers) >= num_questions:
                    break

                # Lấy 4 bubbles cho câu hỏi này
                bubble_start = q * 4
                bubble_end = min(bubble_start + 4, len(row_bubbles))

                if bubble_start >= len(row_bubbles):
                    answers.append("")
                    continue

                question_bubbles = row_bubbles[bubble_start:bubble_end]

                # Tìm bubble được tô đậm nhất
                selected_answer = self.find_selected_bubble(
                    question_bubbles, warped_thresh
                )
                answers.append(selected_answer)

        # Đảm bảo có đủ 40 câu trả lời
        while len(answers) < num_questions:
            answers.append("")

        return answers[:num_questions]

    def find_selected_bubble(self, question_bubbles, warped_thresh):
        """Tìm bubble được chọn trong một câu hỏi"""
        if not question_bubbles:
            return ""

        max_filled_ratio = 0
        selected_choice = ""

        for i, bubble in enumerate(question_bubbles):
            # Tính tỷ lệ tô đen
            filled_ratio = self.calculate_fill_ratio(bubble, warped_thresh)

            # Nếu tỷ lệ tô đen > 40% và là cao nhất
            if filled_ratio > 0.4 and filled_ratio > max_filled_ratio:
                max_filled_ratio = filled_ratio
                selected_choice = (
                    self.answer_choices[i] if i < len(self.answer_choices) else ""
                )

        return selected_choice

    def calculate_fill_ratio(self, contour, binary_image):
        """Tính tỷ lệ tô đen của một contour"""
        # Tạo mask cho contour
        mask = np.zeros(binary_image.shape, dtype="uint8")
        cv2.drawContours(mask, [contour], -1, (255,), -1)

        # Tính số pixel được tô trong vùng mask
        masked = cv2.bitwise_and(binary_image, binary_image, mask=mask)
        total_pixels = cv2.countNonZero(mask)
        filled_pixels = cv2.countNonZero(masked)

        if total_pixels == 0:
            return 0

        return filled_pixels / total_pixels

    def process_answer_sheet(self, image_path: str) -> Dict:
        """Xử lý một phiếu trả lời hoàn chỉnh"""
        try:
            # Đọc ảnh
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Không thể đọc ảnh: {image_path}")

            # Tiền xử lý ảnh
            _, _, thresh = self.preprocess_image(image)

            # Phát hiện contour tài liệu
            paper_contour, approx = self.detect_document_contour(thresh)

            if paper_contour is not None and approx is not None:
                # Thực hiện perspective transform
                if len(approx) >= 4:
                    corners = approx[:4].reshape(4, 2)
                    warped_thresh = self.four_point_transform(thresh, corners)
                else:
                    # Sử dụng rotated rectangle nếu không tìm thấy 4 góc rõ ràng
                    rect = cv2.minAreaRect(paper_contour)
                    box = cv2.boxPoints(rect)
                    corners = self.find_corner_by_rotated_rect(box, approx)
                    warped_thresh = self.four_point_transform(thresh, corners)
            else:
                # Không tìm thấy contour tài liệu, sử dụng ảnh gốc
                warped_thresh = thresh

            # Phát hiện các bubble trả lời
            bubble_contours = self.detect_answer_bubbles(warped_thresh)

            # Trích xuất câu trả lời
            answers = self.extract_answers_from_bubbles(bubble_contours, warped_thresh)

            # Trích xuất thông tin sinh viên (đơn giản hóa)
            student_id = self.extract_student_info(warped_thresh, "student_id")
            test_code = self.extract_student_info(warped_thresh, "test_code")

            return {
                "student_id": student_id,
                "test_code": test_code,
                "answers": answers,
                "total_bubbles_detected": len(bubble_contours),
                "status": "success",
                "error": None,
            }

        except Exception as e:
            logger.error(f"Lỗi xử lý phiếu trả lời {image_path}: {str(e)}")
            return {
                "student_id": "00000000",
                "test_code": "0000",
                "answers": [""] * 40,
                "total_bubbles_detected": 0,
                "status": "error",
                "error": str(e),
            }

    def extract_student_info(self, warped_thresh, info_type="student_id"):
        """Trích xuất thông tin sinh viên (số báo danh, mã đề)"""
        # Đơn giản hóa - trong thực tế cần logic phức tạp hơn
        # để phát hiện vùng thông tin sinh viên và đọc các số

        if info_type == "student_id":
            return "00000000"  # Default student ID
        elif info_type == "test_code":
            return "0000"  # Default test code
        else:
            return ""

    def load_answer_keys_from_excel(self, excel_path: str) -> Dict[str, List[str]]:
        """Đọc đáp án từ file Excel"""
        try:
            df = pd.read_excel(excel_path)

            # Kiểm tra cột 'Mã Đề'
            if "Mã Đề" not in df.columns:
                raise ValueError("File Excel phải có cột 'Mã Đề'")

            answer_keys = {}

            for _, row in df.iterrows():
                test_code = str(row["Mã Đề"]).strip()

                # Đọc 40 câu trả lời
                answers = []
                for i in range(1, 41):  # Câu 1 đến 40
                    col_name = f"Câu {i}"
                    if col_name in df.columns:
                        answer = str(row[col_name]).strip().upper()
                        answers.append(answer if answer in self.answer_choices else "")
                    else:
                        answers.append("")

                answer_keys[test_code] = answers

            logger.info(f"Đã đọc đáp án cho {len(answer_keys)} mã đề")
            return answer_keys

        except Exception as e:
            logger.error(f"Lỗi đọc file Excel: {str(e)}")
            return {}

    def grade_single_sheet(
        self, student_answers: List[str], correct_answers: List[str]
    ) -> Dict:
        """Chấm điểm một phiếu trả lời"""
        if len(student_answers) != len(correct_answers):
            logger.warning(
                f"Số câu trả lời ({len(student_answers)}) không khớp với đáp án ({len(correct_answers)})"
            )

        correct_count = 0
        incorrect_count = 0
        blank_count = 0
        details = []

        for i, (student_ans, correct_ans) in enumerate(
            zip(student_answers, correct_answers)
        ):
            question_num = i + 1

            if not student_ans:  # Câu bỏ trống
                status = "blank"
                blank_count += 1
            elif student_ans == correct_ans:  # Câu đúng
                status = "correct"
                correct_count += 1
            else:  # Câu sai
                status = "incorrect"
                incorrect_count += 1

            details.append(
                {
                    "question": question_num,
                    "student_answer": student_ans,
                    "correct_answer": correct_ans,
                    "status": status,
                }
            )

        # Tính điểm (thang điểm 10)
        total_questions = len(correct_answers)
        score = (correct_count / total_questions) * 10 if total_questions > 0 else 0

        return {
            "score": round(score, 2),
            "correct_count": correct_count,
            "incorrect_count": incorrect_count,
            "blank_count": blank_count,
            "total_questions": total_questions,
            "accuracy_percentage": round((correct_count / total_questions) * 100, 2)
            if total_questions > 0
            else 0,
            "details": details,
        }

    async def batch_process_sheets(self, image_files: List, excel_file) -> List[Dict]:
        """Xử lý hàng loạt phiếu trả lời"""
        results = []

        # Lưu file Excel tạm thời
        with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as temp_excel:
            content = await excel_file.read()
            temp_excel.write(content)
            temp_excel_path = temp_excel.name

        try:
            # Đọc đáp án
            answer_keys = self.load_answer_keys_from_excel(temp_excel_path)

            if not answer_keys:
                raise ValueError("Không thể đọc đáp án từ file Excel")

            logger.info(f"Bắt đầu xử lý {len(image_files)} phiếu trả lời")

            # Xử lý từng ảnh
            for idx, image_file in enumerate(image_files):
                logger.info(
                    f"Đang xử lý phiếu {idx + 1}/{len(image_files)}: {image_file.filename}"
                )

                # Lưu ảnh tạm thời
                with tempfile.NamedTemporaryFile(
                    delete=False, suffix=".jpg"
                ) as temp_image:
                    content = await image_file.read()
                    temp_image.write(content)
                    temp_image_path = temp_image.name

                try:
                    # Xử lý phiếu trả lời
                    extracted_data = self.process_answer_sheet(temp_image_path)

                    if extracted_data["status"] == "success":
                        test_code = extracted_data["test_code"]
                        student_answers = extracted_data["answers"]

                        # Tìm đáp án tương ứng
                        if test_code in answer_keys:
                            correct_answers = answer_keys[test_code]
                        else:
                            # Sử dụng đáp án đầu tiên nếu không tìm thấy mã đề
                            first_key = list(answer_keys.keys())[0]
                            correct_answers = answer_keys[first_key]
                            logger.warning(
                                f"Không tìm thấy đáp án cho mã đề {test_code}, sử dụng mã đề {first_key}"
                            )

                        # Chấm điểm
                        grading_result = self.grade_single_sheet(
                            student_answers, correct_answers
                        )

                        result = {
                            "filename": image_file.filename,
                            "student_id": extracted_data["student_id"],
                            "test_code": test_code,
                            "bubbles_detected": extracted_data[
                                "total_bubbles_detected"
                            ],
                            "grading": grading_result,
                            "status": "success",
                        }

                        if test_code not in answer_keys:
                            result["warning"] = (
                                f"Sử dụng đáp án mặc định cho mã đề {test_code}"
                            )
                    else:
                        result = {
                            "filename": image_file.filename,
                            "student_id": "00000000",
                            "test_code": "0000",
                            "bubbles_detected": 0,
                            "grading": None,
                            "status": "error",
                            "error": extracted_data["error"],
                        }

                    results.append(result)

                finally:
                    # Xóa file ảnh tạm
                    if os.path.exists(temp_image_path):
                        os.unlink(temp_image_path)

            logger.info(f"Hoàn thành xử lý {len(results)} phiếu trả lời")

        finally:
            # Xóa file Excel tạm
            if os.path.exists(temp_excel_path):
                os.unlink(temp_excel_path)

        return results
