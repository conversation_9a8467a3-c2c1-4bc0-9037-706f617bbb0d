import requests
import json
from pathlib import Path

def test_auto_grading_health():
    """Test health check endpoint"""
    print("🔍 Testing Auto Grading Health Check...")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/auto_grading/health")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check successful!")
            print(f"Service: {data['service']}")
            print(f"Status: {data['status']}")
            print("Available processors:")
            for processor in data['processors']:
                print(f"  - {processor['name']} ({processor['endpoint']})")
                print(f"    Features: {', '.join(processor['features'])}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing health check: {str(e)}")
        return False

def test_auto_grading_with_sample_data():
    """Test auto grading with sample data"""
    print("\n🔍 Testing Auto Grading with Sample Data...")
    
    # Kiểm tra file Excel mẫu
    excel_file = Path("data/sample_answer_keys_omr.xlsx")
    if not excel_file.exists():
        print(f"❌ Sample Excel file not found: {excel_file}")
        return False
    
    # Kiểm tra thư mục ảnh test
    test_images_dir = Path("data/test_images")
    if not test_images_dir.exists():
        print(f"⚠️ Test images directory not found: {test_images_dir}")
        print("Creating sample test directory...")
        test_images_dir.mkdir(parents=True, exist_ok=True)
        print("📝 Please add some test images to data/test_images/ directory")
        return False
    
    # Tìm ảnh test
    image_files = list(test_images_dir.glob("*.jpg")) + list(test_images_dir.glob("*.png"))
    if not image_files:
        print("⚠️ No test images found in data/test_images/")
        print("📝 Please add some test images (.jpg or .png) to data/test_images/ directory")
        return False
    
    print(f"Found {len(image_files)} test images")
    
    try:
        # Chuẩn bị files để upload
        files = []
        
        # Thêm file Excel
        with open(excel_file, 'rb') as f:
            files.append(('excel_file', (excel_file.name, f.read(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')))
        
        # Thêm ảnh (tối đa 3 ảnh để test)
        for img_file in image_files[:3]:
            with open(img_file, 'rb') as f:
                files.append(('image_files', (img_file.name, f.read(), 'image/jpeg')))
        
        print(f"Uploading {len(files)-1} images and 1 Excel file...")
        
        # Test basic grading
        print("\n📊 Testing Basic Grading...")
        response = requests.post(
            "http://localhost:8000/api/v1/auto_grading/grade",
            files=files
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Basic grading successful!")
            print(f"Message: {data['message']}")
            print(f"Summary: {data['summary']}")
            print(f"Results count: {len(data['results'])}")
        else:
            print(f"❌ Basic grading failed: {response.status_code}")
            print(f"Response: {response.text}")
        
        # Test enhanced grading
        print("\n📊 Testing Enhanced Grading...")
        response = requests.post(
            "http://localhost:8000/api/v1/auto_grading/grade-enhanced",
            files=files
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Enhanced grading successful!")
            print(f"Message: {data['message']}")
            print(f"Processor: {data['processor']}")
            print(f"Summary: {data['summary']}")
            print(f"Results count: {len(data['results'])}")
            
            # Hiển thị chi tiết một vài kết quả
            for i, result in enumerate(data['results'][:2]):
                print(f"\n📋 Result {i+1}:")
                print(f"  File: {result['filename']}")
                print(f"  Status: {result['status']}")
                print(f"  Student ID: {result.get('student_id', 'N/A')}")
                print(f"  Test Code: {result.get('test_code', 'N/A')}")
                print(f"  Bubbles Detected: {result.get('bubbles_detected', 'N/A')}")
                if result.get('grading'):
                    grading = result['grading']
                    print(f"  Score: {grading['score']}/10")
                    print(f"  Correct: {grading['correct_count']}/{grading['total_questions']}")
                    print(f"  Accuracy: {grading['accuracy_percentage']}%")
            
            return True
        else:
            print(f"❌ Enhanced grading failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing auto grading: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Auto Grading System Tests")
    print("=" * 50)
    
    # Test 1: Health check
    health_ok = test_auto_grading_health()
    
    if health_ok:
        # Test 2: Auto grading with sample data
        grading_ok = test_auto_grading_with_sample_data()
        
        if grading_ok:
            print("\n🎉 All tests passed successfully!")
        else:
            print("\n⚠️ Some grading tests failed")
    else:
        print("\n❌ Health check failed - service may not be running")
        print("💡 Make sure to start the FastAPI server first:")
        print("   fastapi dev app/main.py")

if __name__ == "__main__":
    main()
