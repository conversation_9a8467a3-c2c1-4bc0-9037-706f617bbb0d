from fastapi import APIRouter, UploadFile, File, HTTPException
from typing import List
from app.services.auto_grading_service import AutoGradingService
from app.services.enhanced_omr_processor import EnhancedOMRProcessor
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auto_grading", tags=["Auto Grading"])

# Khởi tạo services
grading_service = AutoGradingService()
enhanced_omr_processor = EnhancedOMRProcessor()


@router.post("/grade")
async def auto_grading_endpoint(
    image_files: List[UploadFile] = File(..., description="Danh sách ảnh bài làm"),
    excel_file: UploadFile = File(..., description="File Excel chứa đáp án"),
):
    """
    API chấm điểm tự động sử dụng OpenCV.

    Xử lý:
    - Số báo danh (8 chữ số)
    - Mã đề thi (4 chữ số)
    - 40 câu trắc nghiệm phần 1

    File Excel cần có:
    - Cột 'Mã Đề': mã đề thi
    - Cột 'Câu 1' đến 'Câu 40': đáp án A, B, C, D
    """
    try:
        # Kiểm tra file upload
        if not image_files:
            raise HTTPException(status_code=400, detail="Cần ít nhất một ảnh bài làm")

        if not excel_file:
            raise HTTPException(status_code=400, detail="Cần file Excel đáp án")

        # Kiểm tra định dạng file Excel
        if not excel_file.filename or not excel_file.filename.endswith(
            (".xlsx", ".xls")
        ):
            raise HTTPException(
                status_code=400, detail="File đáp án phải là Excel (.xlsx hoặc .xls)"
            )

        # Kiểm tra định dạng file ảnh
        allowed_extensions = (".jpg", ".jpeg", ".png", ".bmp", ".tiff")
        for image_file in image_files:
            if not image_file.filename or not any(
                image_file.filename.lower().endswith(ext) for ext in allowed_extensions
            ):
                raise HTTPException(
                    status_code=400,
                    detail=f"File {image_file.filename or 'Unknown'} không phải định dạng ảnh hợp lệ",
                )

        logger.info(f"Bắt đầu chấm điểm {len(image_files)} bài làm")

        # Thực hiện chấm điểm
        results = await grading_service.batch_grade_images(image_files, excel_file)

        # Thống kê kết quả
        total_processed = len(results)
        successful = len([r for r in results if r["status"] == "success"])
        failed = total_processed - successful

        # Tính điểm trung bình
        scores = [
            r["grading"]["score"]
            for r in results
            if r["status"] == "success" and r["grading"]
        ]
        average_score = sum(scores) / len(scores) if scores else 0

        logger.info(f"Hoàn thành chấm điểm: {successful}/{total_processed} thành công")

        return {
            "message": "Chấm điểm hoàn thành",
            "summary": {
                "total_processed": total_processed,
                "successful": successful,
                "failed": failed,
                "average_score": round(average_score, 2),
            },
            "results": results,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Lỗi trong quá trình chấm điểm: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi hệ thống: {str(e)}")


@router.post("/grade-enhanced")
async def enhanced_grading_endpoint(
    image_files: List[UploadFile] = File(..., description="Danh sách ảnh bài làm"),
    excel_file: UploadFile = File(..., description="File Excel chứa đáp án"),
):
    """
    API chấm điểm tự động nâng cao sử dụng Enhanced OMR Processor.

    Dựa trên thuật toán MiAI_Auto_Grading với các cải tiến:
    - Perspective correction tự động
    - Phát hiện bubble chính xác hơn
    - Xử lý nhiều loại ảnh khác nhau
    - Tính toán fill ratio để xác định bubble được chọn

    File Excel cần có:
    - Cột 'Mã Đề': mã đề thi
    - Cột 'Câu 1' đến 'Câu 40': đáp án A, B, C, D
    """
    try:
        # Kiểm tra file upload
        if not image_files:
            raise HTTPException(status_code=400, detail="Cần ít nhất một ảnh bài làm")

        if not excel_file:
            raise HTTPException(status_code=400, detail="Cần file Excel đáp án")

        # Kiểm tra định dạng file Excel
        if not excel_file.filename or not excel_file.filename.endswith(
            (".xlsx", ".xls")
        ):
            raise HTTPException(
                status_code=400, detail="File đáp án phải là Excel (.xlsx hoặc .xls)"
            )

        # Kiểm tra định dạng file ảnh
        allowed_extensions = (".jpg", ".jpeg", ".png", ".bmp", ".tiff")
        for image_file in image_files:
            if not image_file.filename or not any(
                image_file.filename.lower().endswith(ext) for ext in allowed_extensions
            ):
                raise HTTPException(
                    status_code=400,
                    detail=f"File {image_file.filename or 'Unknown'} không phải định dạng ảnh hợp lệ",
                )

        logger.info(f"Bắt đầu chấm điểm nâng cao {len(image_files)} bài làm")

        # Thực hiện chấm điểm với Enhanced OMR Processor
        results = await enhanced_omr_processor.batch_process_sheets(
            image_files, excel_file
        )

        # Thống kê kết quả
        total_processed = len(results)
        successful = len([r for r in results if r["status"] == "success"])
        failed = total_processed - successful

        # Tính điểm trung bình và thống kê bubbles
        scores = [
            r["grading"]["score"]
            for r in results
            if r["status"] == "success" and r["grading"]
        ]
        average_score = sum(scores) / len(scores) if scores else 0

        total_bubbles = sum([r.get("bubbles_detected", 0) for r in results])
        avg_bubbles_per_sheet = (
            total_bubbles / total_processed if total_processed > 0 else 0
        )

        logger.info(
            f"Hoàn thành chấm điểm nâng cao: {successful}/{total_processed} thành công"
        )

        return {
            "message": "Chấm điểm nâng cao hoàn thành",
            "processor": "Enhanced OMR Processor (MiAI-based)",
            "summary": {
                "total_processed": total_processed,
                "successful": successful,
                "failed": failed,
                "average_score": round(average_score, 2),
                "total_bubbles_detected": total_bubbles,
                "avg_bubbles_per_sheet": round(avg_bubbles_per_sheet, 1),
            },
            "results": results,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Lỗi trong quá trình chấm điểm nâng cao: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi hệ thống: {str(e)}")


@router.get("/health")
async def health_check():
    """Kiểm tra trạng thái service chấm điểm"""
    return {
        "status": "healthy",
        "service": "Auto Grading Service",
        "processors": [
            {
                "name": "Basic Auto Grading Service",
                "endpoint": "/grade",
                "features": ["Trích xuất cơ bản", "Chấm điểm đơn giản"],
            },
            {
                "name": "Enhanced OMR Processor",
                "endpoint": "/grade-enhanced",
                "features": [
                    "Perspective correction tự động",
                    "Phát hiện bubble chính xác",
                    "Tính toán fill ratio",
                    "Xử lý đa dạng ảnh",
                    "Dựa trên MiAI_Auto_Grading",
                ],
            },
        ],
        "supported_formats": {
            "images": [".jpg", ".jpeg", ".png", ".bmp", ".tiff"],
            "answer_keys": [".xlsx", ".xls"],
        },
    }
