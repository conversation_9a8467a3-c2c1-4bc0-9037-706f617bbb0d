import cv2
import numpy as np
import pandas as pd
import math
from typing import List, Dict, Tuple, Optional
import logging
from pathlib import Path
import tempfile
import os

logger = logging.getLogger(__name__)


class AutoGradingService:
    """
    Service chấm điểm tự động sử dụng OpenCV
    Xử lý số báo danh, mã đề thi và 40 câu trắc nghiệm phần 1
    """

    def __init__(self):
        self.answer_choices = ["A", "B", "C", "D"]

    def distance(self, p1: Tuple[int, int], p2: Tuple[int, int]) -> float:
        """T<PERSON>h khoảng cách giữa hai điểm"""
        return math.sqrt(((p1[0] - p2[0]) ** 2) + ((p1[1] - p2[1]) ** 2))

    def sort_contours(self, cnts, method="left-to-right"):
        """Sắp xếp contours theo phương pháp chỉ định"""
        reverse = False
        i = 0

        if method == "right-to-left" or method == "bottom-to-top":
            reverse = True

        if method == "top-to-bottom" or method == "bottom-to-top":
            i = 1

        bounding_boxes = [cv2.boundingRect(c) for c in cnts]
        (cnts, bounding_boxes) = zip(
            *sorted(zip(cnts, bounding_boxes), key=lambda b: b[1][i], reverse=reverse)
        )

        return cnts, bounding_boxes

    def order_points(self, pts):
        """Sắp xếp 4 điểm theo thứ tự: top-left, top-right, bottom-right, bottom-left"""
        rect = np.zeros((4, 2), dtype="float32")

        s = pts.sum(axis=1)
        rect[0] = pts[np.argmin(s)]  # top-left
        rect[2] = pts[np.argmax(s)]  # bottom-right

        diff = np.diff(pts, axis=1)
        rect[1] = pts[np.argmin(diff)]  # top-right
        rect[3] = pts[np.argmax(diff)]  # bottom-left

        return rect

    def four_point_transform(self, image, pts):
        """Thực hiện perspective transform"""
        rect = self.order_points(pts)
        (tl, tr, br, bl) = rect

        # Tính chiều rộng
        width_a = np.sqrt(((br[0] - bl[0]) ** 2) + ((br[1] - bl[1]) ** 2))
        width_b = np.sqrt(((tr[0] - tl[0]) ** 2) + ((tr[1] - tl[1]) ** 2))
        max_width = max(int(width_a), int(width_b))

        # Tính chiều cao
        height_a = np.sqrt(((tr[0] - br[0]) ** 2) + ((tr[1] - br[1]) ** 2))
        height_b = np.sqrt(((tl[0] - bl[0]) ** 2) + ((tl[1] - bl[1]) ** 2))
        max_height = max(int(height_a), int(height_b))

        # Điểm đích
        dst = np.array(
            [
                [0, 0],
                [max_width - 1, 0],
                [max_width - 1, max_height - 1],
                [0, max_height - 1],
            ],
            dtype="float32",
        )

        # Thực hiện transform
        M = cv2.getPerspectiveTransform(rect, dst)
        warped = cv2.warpPerspective(image, M, (max_width, max_height))

        return warped

    def find_corner_by_rotated_rect(self, box, approx):
        """Tìm góc dựa trên rotated rectangle"""
        corner = []
        for p_box in box:
            min_dist = 999999999
            min_p = None
            for p in approx:
                dist = self.distance(p_box, p[0])
                if dist < min_dist:
                    min_dist = dist
                    min_p = p[0]
            corner.append(min_p)

        return np.array(corner)

    def preprocess_image(self, image):
        """Tiền xử lý ảnh"""
        # Chuyển sang grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Làm mờ để giảm noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Threshold adaptive
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 31, 3
        )

        return gray, thresh

    def detect_paper_contour(self, thresh):
        """Phát hiện contour của tờ giấy"""
        contours, _ = cv2.findContours(thresh, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        contours = sorted(contours, key=lambda x: cv2.contourArea(x), reverse=True)

        # Tìm contour lớn nhất có 4 góc (tờ giấy)
        for contour in contours[:5]:  # Kiểm tra 5 contour lớn nhất
            peri = cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, 0.02 * peri, True)

            if len(approx) == 4:
                return contour, approx

        # Nếu không tìm thấy, sử dụng contour lớn nhất
        if len(contours) > 0:
            contour = contours[0]
            rect = cv2.minAreaRect(contour)
            box = cv2.boxPoints(rect)
            approx = np.array([[point] for point in box], dtype=np.int32)
            return contour, approx

        return None, None

    def extract_student_info_region(self, image):
        """Trích xuất vùng thông tin sinh viên (góc trên phải)"""
        height, width = image.shape[:2]

        # Vùng góc trên phải (30% chiều rộng, 20% chiều cao)
        start_x = int(width * 0.7)
        end_x = width
        start_y = 0
        end_y = int(height * 0.2)

        return image[start_y:end_y, start_x:end_x]

    def extract_answers_region(self, image):
        """Trích xuất vùng 40 câu trắc nghiệm phần 1"""
        height, width = image.shape[:2]

        # Vùng câu trả lời (toàn bộ chiều rộng, từ 20% đến 80% chiều cao)
        start_x = 0
        end_x = width
        start_y = int(height * 0.2)
        end_y = int(height * 0.8)

        return image[start_y:end_y, start_x:end_x]

    def detect_filled_bubbles(self, region, min_area=50, max_area=200):
        """Phát hiện các ô tròn đã tô"""
        contours, _ = cv2.findContours(
            region, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )

        bubble_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if min_area <= area <= max_area:
                # Kiểm tra tỷ lệ khung hình (gần tròn)
                (_, _, w, h) = cv2.boundingRect(contour)
                aspect_ratio = w / float(h)

                if 0.8 <= aspect_ratio <= 1.2:
                    bubble_contours.append(contour)

        return bubble_contours

    def extract_student_id(self, student_info_region):
        """Trích xuất số báo danh từ vùng thông tin sinh viên"""
        # Tìm các ô tròn trong vùng này
        bubbles = self.detect_filled_bubbles(student_info_region)

        if not bubbles:
            return "00000000"  # Default value

        # Sắp xếp theo vị trí để tạo thành số báo danh
        bubbles = self.sort_contours(bubbles, method="left-to-right")[0]

        # Giả định có 8 chữ số, mỗi chữ số có 10 ô (0-9)
        student_id = ""
        for i in range(min(8, len(bubbles))):
            # Tìm ô được tô trong cột này
            digit = i % 10  # Simplified logic
            student_id += str(digit)

        return student_id.ljust(8, "0")

    def extract_test_code(self, student_info_region):
        """Trích xuất mã đề thi từ vùng thông tin sinh viên"""
        # Tương tự như extract_student_id nhưng cho mã đề
        bubbles = self.detect_filled_bubbles(student_info_region)

        if not bubbles:
            return "0000"  # Default value

        # Giả định có 4 chữ số cho mã đề
        return "0000"  # Simplified for now

    def extract_answers(self, answers_region):
        """Trích xuất 40 câu trả lời từ vùng câu hỏi"""
        bubbles = self.detect_filled_bubbles(answers_region)

        if not bubbles:
            return [""] * 40

        # Sắp xếp bubbles theo vị trí
        bubbles = self.sort_contours(bubbles, method="top-to-bottom")[0]

        answers = []

        # Xử lý theo từng hàng (giả định 10 hàng x 4 cột cho 40 câu)
        for row in range(10):
            for col in range(4):
                question_idx = row * 4 + col
                if question_idx >= 40:
                    break

                # Tìm câu trả lời cho câu hỏi này
                answer = self.find_selected_answer(bubbles, row, col)
                answers.append(answer)

        return answers[:40]

    def find_selected_answer(self, bubbles, row, col):
        """Tìm đáp án được chọn cho một câu hỏi cụ thể"""
        # Logic đơn giản hóa - cần cải thiện dựa trên layout thực tế
        if bubbles and len(bubbles) > row * 4 + col:
            return self.answer_choices[col % 4]
        return ""

    def calculate_fill_ratio(self, contour, binary_image):
        """Tính tỷ lệ tô đen của một contour"""
        mask = np.zeros(binary_image.shape, dtype="uint8")
        cv2.drawContours(mask, [contour], -1, (255,), -1)

        # Tính số pixel trắng trong mask
        masked = cv2.bitwise_and(binary_image, binary_image, mask=mask)
        total_pixels = cv2.countNonZero(mask)
        filled_pixels = cv2.countNonZero(masked)

        if total_pixels == 0:
            return 0

        return filled_pixels / total_pixels

    def process_single_image(self, image_path: str) -> Dict:
        """Xử lý một ảnh bài làm"""
        try:
            # Đọc ảnh
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Không thể đọc ảnh: {image_path}")

            # Tiền xử lý
            _, thresh = self.preprocess_image(image)

            # Phát hiện contour tờ giấy
            paper_contour, approx = self.detect_paper_contour(thresh)

            if paper_contour is not None and approx is not None:
                # Thực hiện perspective transform
                if len(approx) >= 4:
                    # Sử dụng 4 điểm đầu tiên
                    corners = approx[:4].reshape(4, 2)
                    warped_image = self.four_point_transform(image, corners)
                    warped_thresh = self.four_point_transform(thresh, corners)
                else:
                    warped_image = image
                    warped_thresh = thresh
            else:
                warped_image = image
                warped_thresh = thresh

            # Trích xuất các vùng
            student_info_region = self.extract_student_info_region(warped_thresh)
            answers_region = self.extract_answers_region(warped_thresh)

            # Trích xuất thông tin
            student_id = self.extract_student_id(student_info_region)
            test_code = self.extract_test_code(student_info_region)
            answers = self.extract_answers(answers_region)

            return {
                "student_id": student_id,
                "test_code": test_code,
                "answers": answers,
                "status": "success",
                "error": None,
            }

        except Exception as e:
            logger.error(f"Lỗi xử lý ảnh {image_path}: {str(e)}")
            return {
                "student_id": "00000000",
                "test_code": "0000",
                "answers": [""] * 40,
                "status": "error",
                "error": str(e),
            }

    def load_answer_keys(self, excel_path: str) -> Dict[str, List[str]]:
        """Đọc đáp án từ file Excel"""
        try:
            df = pd.read_excel(excel_path)

            # Kiểm tra cột 'Mã Đề'
            if "Mã Đề" not in df.columns:
                raise ValueError("File Excel phải có cột 'Mã Đề'")

            answer_keys = {}

            for _, row in df.iterrows():
                test_code = str(row["Mã Đề"]).strip()

                # Đọc 40 câu trả lời
                answers = []
                for i in range(1, 41):  # Câu 1 đến 40
                    col_name = f"Câu {i}"
                    if col_name in df.columns:
                        answer = str(row[col_name]).strip().upper()
                        answers.append(answer if answer in self.answer_choices else "")
                    else:
                        answers.append("")

                answer_keys[test_code] = answers

            return answer_keys

        except Exception as e:
            logger.error(f"Lỗi đọc file Excel: {str(e)}")
            return {}

    def grade_answers(
        self, student_answers: List[str], correct_answers: List[str]
    ) -> Dict:
        """Chấm điểm một bài làm"""
        if len(student_answers) != len(correct_answers):
            logger.warning("Số câu trả lời không khớp với đáp án")

        correct_count = 0
        incorrect_count = 0
        blank_count = 0
        details = []

        for i, (student_ans, correct_ans) in enumerate(
            zip(student_answers, correct_answers)
        ):
            question_num = i + 1

            if not student_ans:  # Câu bỏ trống
                status = "blank"
                blank_count += 1
            elif student_ans == correct_ans:  # Câu đúng
                status = "correct"
                correct_count += 1
            else:  # Câu sai
                status = "incorrect"
                incorrect_count += 1

            details.append(
                {
                    "question": question_num,
                    "student_answer": student_ans,
                    "correct_answer": correct_ans,
                    "status": status,
                }
            )

        # Tính điểm (thang điểm 10)
        total_questions = len(correct_answers)
        score = (correct_count / total_questions) * 10 if total_questions > 0 else 0

        return {
            "score": round(score, 2),
            "correct_count": correct_count,
            "incorrect_count": incorrect_count,
            "blank_count": blank_count,
            "total_questions": total_questions,
            "details": details,
        }

    async def batch_grade_images(self, image_files: List, excel_file) -> List[Dict]:
        """Chấm điểm hàng loạt"""
        results = []

        # Lưu file Excel tạm thời
        with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as temp_excel:
            content = await excel_file.read()
            temp_excel.write(content)
            temp_excel_path = temp_excel.name

        try:
            # Đọc đáp án
            answer_keys = self.load_answer_keys(temp_excel_path)

            if not answer_keys:
                raise ValueError("Không thể đọc đáp án từ file Excel")

            # Xử lý từng ảnh
            for image_file in image_files:
                # Lưu ảnh tạm thời
                with tempfile.NamedTemporaryFile(
                    delete=False, suffix=".jpg"
                ) as temp_image:
                    content = await image_file.read()
                    temp_image.write(content)
                    temp_image_path = temp_image.name

                try:
                    # Xử lý ảnh
                    extracted_data = self.process_single_image(temp_image_path)

                    if extracted_data["status"] == "success":
                        test_code = extracted_data["test_code"]
                        student_answers = extracted_data["answers"]

                        # Tìm đáp án tương ứng
                        if test_code in answer_keys:
                            correct_answers = answer_keys[test_code]
                            grading_result = self.grade_answers(
                                student_answers, correct_answers
                            )
                        else:
                            # Sử dụng đáp án đầu tiên nếu không tìm thấy mã đề
                            first_key = list(answer_keys.keys())[0]
                            correct_answers = answer_keys[first_key]
                            grading_result = self.grade_answers(
                                student_answers, correct_answers
                            )
                            grading_result["warning"] = (
                                f"Không tìm thấy đáp án cho mã đề {test_code}, sử dụng mã đề {first_key}"
                            )

                        result = {
                            "filename": image_file.filename,
                            "student_id": extracted_data["student_id"],
                            "test_code": test_code,
                            "grading": grading_result,
                            "status": "success",
                        }
                    else:
                        result = {
                            "filename": image_file.filename,
                            "student_id": "00000000",
                            "test_code": "0000",
                            "grading": None,
                            "status": "error",
                            "error": extracted_data["error"],
                        }

                    results.append(result)

                finally:
                    # Xóa file ảnh tạm
                    if os.path.exists(temp_image_path):
                        os.unlink(temp_image_path)

        finally:
            # Xóa file Excel tạm
            if os.path.exists(temp_excel_path):
                os.unlink(temp_excel_path)

        return results
