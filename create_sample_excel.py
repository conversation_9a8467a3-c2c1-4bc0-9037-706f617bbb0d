import pandas as pd

# Tạo dữ liệu mẫu cho file Excel đáp án
data = {
    'Mã Đề': ['001', '002', '003', '004']
}

# Thêm 40 câu hỏi
for i in range(1, 41):
    col_name = f'Câu {i}'
    # Tạo pattern đáp án khác nhau cho mỗi mã đề
    answers = []
    for test_code_idx in range(4):
        answer_idx = (i - 1 + test_code_idx) % 4
        answer = ['A', 'B', 'C', 'D'][answer_idx]
        answers.append(answer)
    data[col_name] = answers

# Tạo DataFrame và lưu thành Excel
df = pd.DataFrame(data)
df.to_excel('data/sample_answer_keys_omr.xlsx', index=False)

print("✅ Đã tạo file Excel mẫu: data/sample_answer_keys_omr.xlsx")
print("Nội dung file:")
print(df.head())
